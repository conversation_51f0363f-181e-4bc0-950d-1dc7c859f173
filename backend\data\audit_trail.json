[{"id": "AUD_20250620_094012_15832", "timestamp": "2025-06-20T09:40:12.359978", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_094012_15832", "timestamp": "2025-06-20T09:40:12.397156", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1, "report_id": 1, "sid_number": "AT001", "total_tests": 5, "matched_tests": 0, "unmatched_tests": 5, "test_match_rate": 0.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100218_13496", "timestamp": "2025-06-20T10:02:18.156694", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 40}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100218_13496", "timestamp": "2025-06-20T10:02:18.171018", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 40, "report_id": 2, "sid_number": "AM005", "total_tests": 1, "matched_tests": 0, "unmatched_tests": 1, "test_match_rate": 0.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100625_18780", "timestamp": "2025-06-20T10:06:25.664359", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 40}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100625_18780", "timestamp": "2025-06-20T10:06:25.687046", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 40, "report_id": 3, "sid_number": "AM006", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100922_28116", "timestamp": "2025-06-20T10:09:22.744100", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100922_28116", "timestamp": "2025-06-20T10:09:22.791228", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1, "report_id": 3, "sid_number": "AT001", "total_tests": 5, "matched_tests": 2, "unmatched_tests": 3, "test_match_rate": 0.4}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_101408_8256", "timestamp": "2025-06-20T10:14:08.734654", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_101408_8256", "timestamp": "2025-06-20T10:14:08.768853", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1, "report_id": 3, "sid_number": "AT001", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_101408_8256", "timestamp": "2025-06-20T10:14:08.768853", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 40}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_101408_8256", "timestamp": "2025-06-20T10:14:08.785801", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 40, "report_id": 3, "sid_number": "AM006", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_102150_7340", "timestamp": "2025-06-20T10:21:50.524740", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 1}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_102150_7340", "timestamp": "2025-06-20T10:21:50.555107", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 1, "report_id": 3, "sid_number": "AT001", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_104633_23384", "timestamp": "2025-06-20T10:46:33.289953", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 41}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_104633_23384", "timestamp": "2025-06-20T10:46:33.313062", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 41, "report_id": 3, "sid_number": "AM006", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_111419_5696", "timestamp": "2025-06-20T11:14:19.887163", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 42}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_111419_5696", "timestamp": "2025-06-20T11:14:19.905952", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 42, "report_id": 4, "sid_number": "AM007", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_135354_18056", "timestamp": "2025-06-20T13:53:54.188986", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 43}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_135354_18056", "timestamp": "2025-06-20T13:53:54.239705", "event_type": "report_generation_success", "user_id": null, "tenant_id": 2, "success": true, "details": {"billing_id": 43, "report_id": 5, "sid_number": "AS001", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_142726_13948", "timestamp": "2025-06-20T14:27:26.050576", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 44}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_142726_13948", "timestamp": "2025-06-20T14:27:26.085895", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 44, "report_id": 6, "sid_number": "AM008", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_143457_13948", "timestamp": "2025-06-20T14:34:57.272175", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 45}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_143457_13948", "timestamp": "2025-06-20T14:34:57.297724", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 45, "report_id": 7, "sid_number": "AM009", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_143909_13948", "timestamp": "2025-06-20T14:39:09.115327", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 46}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_143909_13948", "timestamp": "2025-06-20T14:39:09.147679", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 46, "report_id": 8, "sid_number": "AM010", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_143950_13948", "timestamp": "2025-06-20T14:39:50.426860", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 47}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_143950_13948", "timestamp": "2025-06-20T14:39:50.460015", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 47, "report_id": 9, "sid_number": "AM011", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_144041_13948", "timestamp": "2025-06-20T14:40:41.460647", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 48}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_144041_13948", "timestamp": "2025-06-20T14:40:41.486402", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 48, "report_id": 10, "sid_number": "AM012", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_144429_13948", "timestamp": "2025-06-20T14:44:29.528431", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 49}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_144429_13948", "timestamp": "2025-06-20T14:44:29.545408", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 49, "report_id": 11, "sid_number": "AM013", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153108_26876", "timestamp": "2025-06-20T15:31:08.370189", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 50}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153108_26876", "timestamp": "2025-06-20T15:31:08.434000", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 50, "report_id": 12, "sid_number": "AM014", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153619_8552", "timestamp": "2025-06-20T15:36:19.601703", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 51}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153619_8552", "timestamp": "2025-06-20T15:36:19.617901", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 51, "report_id": 13, "sid_number": "AM015", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153722_25976", "timestamp": "2025-06-20T15:37:22.323484", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 52}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153722_25976", "timestamp": "2025-06-20T15:37:22.391376", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 52, "report_id": 14, "sid_number": "AM016", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153832_25976", "timestamp": "2025-06-20T15:38:32.141721", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 53}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_153832_25976", "timestamp": "2025-06-20T15:38:32.171896", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 53, "report_id": 15, "sid_number": "AM017", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.241511", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 3, "success": true, "details": {"billing_id": 2}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.307493", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 3, "success": true, "details": {"billing_id": 2, "report_id": 16, "sid_number": "AT001", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.326786", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 3}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.360412", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 3, "report_id": 17, "sid_number": "AT002", "total_tests": 4, "matched_tests": 4, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.377569", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 2, "success": true, "details": {"billing_id": 4}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.391736", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 2, "success": true, "details": {"billing_id": 4, "report_id": 18, "sid_number": "AS002", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.394208", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 5}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.427326", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 5, "report_id": 19, "sid_number": "AT003", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.442262", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 6}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.460948", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 6, "report_id": 20, "sid_number": "AM018", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.476660", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 7}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.494566", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 7, "report_id": 21, "sid_number": "AS003", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.510630", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 1, "success": true, "details": {"billing_id": 8}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.544165", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 1, "success": true, "details": {"billing_id": 8, "report_id": 22, "sid_number": "AM019", "total_tests": 4, "matched_tests": 4, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.560435", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 3, "success": true, "details": {"billing_id": 9}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.612077", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 3, "success": true, "details": {"billing_id": 9, "report_id": 23, "sid_number": "AT004", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.629352", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 10}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.676820", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 10, "report_id": 24, "sid_number": "AM020", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.694836", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 11}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.744850", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 11, "report_id": 25, "sid_number": "AS004", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.760126", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 12}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.806067", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 12, "report_id": 26, "sid_number": "AT005", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.824697", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 2, "success": true, "details": {"billing_id": 13}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.860157", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 2, "success": true, "details": {"billing_id": 13, "report_id": 27, "sid_number": "AS005", "total_tests": 4, "matched_tests": 4, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.876549", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 14}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.906122", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 14, "report_id": 28, "sid_number": "AM021", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.924748", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 3, "success": true, "details": {"billing_id": 15}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.944323", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 3, "success": true, "details": {"billing_id": 15, "report_id": 29, "sid_number": "AT006", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.960517", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 3, "success": true, "details": {"billing_id": 16}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:24.978175", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 3, "success": true, "details": {"billing_id": 16, "report_id": 30, "sid_number": "AT007", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154624_27900", "timestamp": "2025-06-20T15:46:25.006083", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 17}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.027320", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 17, "report_id": 31, "sid_number": "AT008", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.045055", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 18}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.061207", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 18, "report_id": 32, "sid_number": "AM022", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.091784", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 19}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.111747", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 19, "report_id": 33, "sid_number": "AM023", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.127208", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 20}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.160758", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 3, "success": true, "details": {"billing_id": 20, "report_id": 34, "sid_number": "AT009", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.176699", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 1, "success": true, "details": {"billing_id": 21}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.224882", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 1, "success": true, "details": {"billing_id": 21, "report_id": 35, "sid_number": "AM024", "total_tests": 4, "matched_tests": 4, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.243913", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 22}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.274715", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 1, "success": true, "details": {"billing_id": 22, "report_id": 36, "sid_number": "AM025", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.306875", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 23}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.342630", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 23, "report_id": 37, "sid_number": "AS006", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.360271", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 24}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.394095", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 24, "report_id": 38, "sid_number": "AS007", "total_tests": 4, "matched_tests": 4, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.428250", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 25}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.468692", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 25, "report_id": 39, "sid_number": "AS008", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.528333", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 1, "success": true, "details": {"billing_id": 26}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.556658", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 1, "success": true, "details": {"billing_id": 26, "report_id": 40, "sid_number": "AM026", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.573210", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 3, "success": true, "details": {"billing_id": 27}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.606670", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 3, "success": true, "details": {"billing_id": 27, "report_id": 41, "sid_number": "AT010", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.639972", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 28}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.656607", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 28, "report_id": 42, "sid_number": "AM027", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.690821", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 29}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.723623", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 29, "report_id": 43, "sid_number": "AM028", "total_tests": 4, "matched_tests": 4, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.756405", "event_type": "report_generation_started", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 30}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.773137", "event_type": "report_generation_success", "user_id": 3, "tenant_id": 2, "success": true, "details": {"billing_id": 30, "report_id": 44, "sid_number": "AS009", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.806507", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 2, "success": true, "details": {"billing_id": 31}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.823260", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 2, "success": true, "details": {"billing_id": 31, "report_id": 45, "sid_number": "AS010", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.855780", "event_type": "report_generation_started", "user_id": 2, "tenant_id": 2, "success": true, "details": {"billing_id": 32}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.889163", "event_type": "report_generation_success", "user_id": 2, "tenant_id": 2, "success": true, "details": {"billing_id": 32, "report_id": 46, "sid_number": "AS011", "total_tests": 1, "matched_tests": 0, "unmatched_tests": 1, "test_match_rate": 0.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.906631", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 33}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.938181", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 33, "report_id": 47, "sid_number": "AM029", "total_tests": 1, "matched_tests": 0, "unmatched_tests": 1, "test_match_rate": 0.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.956494", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 34}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154625_27900", "timestamp": "2025-06-20T15:46:25.973198", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 34, "report_id": 48, "sid_number": "AM030", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.006416", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 35}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.023326", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 35, "report_id": 49, "sid_number": "AM031", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.057659", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 36}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.073338", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 36, "report_id": 50, "sid_number": "AM032", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.106480", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 37}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.138320", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 37, "report_id": 51, "sid_number": "AM033", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.156628", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 38}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.173276", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 38, "report_id": 52, "sid_number": "AM034", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.206493", "event_type": "report_generation_started", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 39}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154626_27900", "timestamp": "2025-06-20T15:46:26.223198", "event_type": "report_generation_success", "user_id": 19, "tenant_id": 1, "success": true, "details": {"billing_id": 39, "report_id": 53, "sid_number": "AM035", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154723_28984", "timestamp": "2025-06-20T15:47:23.487881", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 54}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154723_28984", "timestamp": "2025-06-20T15:47:23.552438", "event_type": "report_generation_success", "user_id": null, "tenant_id": 3, "success": true, "details": {"billing_id": 54, "report_id": 54, "sid_number": "AT011", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154821_28984", "timestamp": "2025-06-20T15:48:21.466702", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 55}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_154821_28984", "timestamp": "2025-06-20T15:48:21.520241", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 55, "report_id": 55, "sid_number": "AM036", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163204_27392", "timestamp": "2025-06-20T16:32:04.498004", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 56}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163204_27392", "timestamp": "2025-06-20T16:32:04.576204", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 56, "report_id": 56, "sid_number": "MYD037", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163303_27392", "timestamp": "2025-06-20T16:33:03.890795", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 57}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163303_27392", "timestamp": "2025-06-20T16:33:03.970525", "event_type": "report_generation_success", "user_id": null, "tenant_id": 3, "success": true, "details": {"billing_id": 57, "report_id": 57, "sid_number": "TNJ014", "total_tests": 2, "matched_tests": 2, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163758_27392", "timestamp": "2025-06-20T16:37:58.033227", "event_type": "report_generation_started", "user_id": null, "tenant_id": null, "success": true, "details": {"billing_id": 58}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163758_27392", "timestamp": "2025-06-20T16:37:58.108584", "event_type": "report_generation_success", "user_id": null, "tenant_id": 1, "success": true, "details": {"billing_id": 58, "report_id": 58, "sid_number": "MYD038", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163801_23960", "timestamp": "2025-06-20T16:38:01.695588", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 58}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_163801_23960", "timestamp": "2025-06-20T16:38:01.737392", "event_type": "report_generation_success", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 58, "report_id": 59, "sid_number": "MYD039", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_215822_27920", "timestamp": "2025-06-20T21:58:22.614664", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 31}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_215822_27920", "timestamp": "2025-06-20T21:58:22.666408", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 31, "report_id": 60, "sid_number": "MYD040", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_124709_27424", "timestamp": "2025-06-21T12:47:09.145759", "event_type": "report_generation_started", "user_id": 5, "tenant_id": 2, "success": true, "details": {"billing_id": 32}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_124709_27424", "timestamp": "2025-06-21T12:47:09.210412", "event_type": "report_generation_success", "user_id": 5, "tenant_id": 2, "success": true, "details": {"billing_id": 32, "report_id": 61, "sid_number": "SKZ012", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_142132_26588", "timestamp": "2025-06-21T14:21:32.522938", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 33}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_142132_26588", "timestamp": "2025-06-21T14:21:32.565072", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 33, "report_id": 62, "sid_number": "MYD041", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_143220_14212", "timestamp": "2025-06-21T14:32:20.748951", "event_type": "report_generation_started", "user_id": 5, "tenant_id": 2, "success": true, "details": {"billing_id": 34}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_143220_14212", "timestamp": "2025-06-21T14:32:20.829936", "event_type": "report_generation_success", "user_id": 5, "tenant_id": 2, "success": true, "details": {"billing_id": 34, "report_id": 63, "sid_number": "SKZ015", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_143323_14212", "timestamp": "2025-06-21T14:33:23.894705", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 35}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_143323_14212", "timestamp": "2025-06-21T14:33:23.949439", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 2, "success": true, "details": {"billing_id": 35, "report_id": 64, "sid_number": "SKZ016", "total_tests": 1, "matched_tests": 1, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_184040_13624", "timestamp": "2025-06-21T18:40:40.104302", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 36}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250621_184040_13624", "timestamp": "2025-06-21T18:40:40.218632", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 36, "report_id": 65, "sid_number": "MYD042", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250627_174617_27140", "timestamp": "2025-06-27T17:46:17.097643", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 37}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250627_174617_27140", "timestamp": "2025-06-27T17:46:17.276337", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 37, "report_id": 66, "sid_number": "MYD043", "total_tests": 6, "matched_tests": 6, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250629_115927_3720", "timestamp": "2025-06-29T11:59:27.336093", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 38}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250629_115927_3720", "timestamp": "2025-06-29T11:59:27.932696", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 38, "report_id": 67, "sid_number": "MYD044", "total_tests": 3, "matched_tests": 3, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250706_121439_61500", "timestamp": "2025-07-06T12:14:39.026972", "event_type": "report_generation_started", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 39}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250706_121439_61500", "timestamp": "2025-07-06T12:14:39.321786", "event_type": "report_generation_success", "user_id": 4, "tenant_id": 1, "success": true, "details": {"billing_id": 39, "report_id": 68, "sid_number": "MYD045", "total_tests": 5, "matched_tests": 5, "unmatched_tests": 0, "test_match_rate": 1.0}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}]