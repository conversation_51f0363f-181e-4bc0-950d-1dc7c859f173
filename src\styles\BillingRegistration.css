/* Billing Registration Styles */
.billing-registration {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.billing-registration .card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.billing-registration .card-header {
  border-radius: 10px 10px 0 0 !important;
  font-weight: 600;
  padding: 15px 20px;
}

.billing-registration .card-body {
  padding: 20px;
}

/* Form Styling */
.billing-registration .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.billing-registration .form-control,
.billing-registration .form-select {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 10px 15px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.billing-registration .form-control:focus,
.billing-registration .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.billing-registration .input-group-text {
  background-color: #e9ecef;
  border: 2px solid #e9ecef;
  border-radius: 8px 0 0 8px;
  font-weight: 600;
}

/* Searchable Dropdown */
.searchable-dropdown {
  position: relative;
}

.searchable-dropdown .dropdown-menu {
  border: 2px solid #007bff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
}

.searchable-dropdown .dropdown-item {
  padding: 10px 15px;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.searchable-dropdown .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.searchable-dropdown .dropdown-item:focus {
  background-color: #007bff;
  color: white;
}

/* Table Styling */
.billing-registration .table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.billing-registration .table thead th {
  background-color: #343a40;
  color: white;
  font-weight: 600;
  border: none;
  padding: 15px;
}

.billing-registration .table tbody td {
  padding: 12px 15px;
  vertical-align: middle;
  border-color: #e9ecef;
}

.billing-registration .table tfoot th {
  background-color: #d1ecf1;
  color: #0c5460;
  font-weight: 600;
  border: none;
  padding: 15px;
}

/* Billing Summary */
.billing-summary {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.billing-summary .fw-bold {
  font-size: 16px;
}

.billing-summary .text-primary {
  color: #007bff !important;
  font-size: 18px;
}

.billing-summary .text-danger {
  color: #dc3545 !important;
  font-size: 16px;
}

/* Button Styling */
.billing-registration .btn {
  border-radius: 8px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
}

.billing-registration .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.billing-registration .btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
}

.billing-registration .btn-success {
  background: linear-gradient(45deg, #28a745, #1e7e34);
}

.billing-registration .btn-danger {
  background: linear-gradient(45deg, #dc3545, #c82333);
}

.billing-registration .btn-warning {
  background: linear-gradient(45deg, #ffc107, #e0a800);
  color: #212529;
}

.billing-registration .btn-info {
  background: linear-gradient(45deg, #17a2b8, #138496);
}

.billing-registration .btn-secondary {
  background: linear-gradient(45deg, #6c757d, #545b62);
}

/* Header Styling */
.billing-registration h1 {
  color: #343a40;
  font-weight: 700;
  margin-bottom: 0;
}

.billing-registration h6 {
  margin-bottom: 0;
  font-size: 16px;
}

/* Alert Styling */
.billing-registration .alert {
  border-radius: 8px;
  border: none;
  padding: 15px 20px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .billing-registration {
    padding: 10px;
  }
  
  .billing-registration .card-body {
    padding: 15px;
  }
  
  .billing-registration .btn {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .billing-registration h1 {
    font-size: 24px;
  }
  
  .billing-summary {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .billing-registration .d-flex.justify-content-between {
    flex-direction: column;
    gap: 10px;
  }
  
  .billing-registration .d-flex.justify-content-end {
    flex-direction: column;
  }
  
  .billing-registration .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* Loading State */
.billing-registration .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Form Validation */
.billing-registration .form-control:invalid {
  border-color: #dc3545;
}

.billing-registration .form-control:valid {
  border-color: #28a745;
}

/* Custom Checkbox Styling */
.billing-registration .form-check-input {
  width: 1.2em;
  height: 1.2em;
  margin-top: 0.1em;
}

.billing-registration .form-check-label {
  font-weight: 500;
  color: #495057;
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.billing-registration .card {
  animation: fadeIn 0.5s ease-out;
}

/* Enhanced Discount System Styles */
.billing-registration .discount-section {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.billing-registration .discount-section .form-label {
  color: #856404;
  font-weight: 600;
}

/* Payment Status Styles */
.billing-registration .payment-status-paid {
  background-color: #d4edda;
  color: #155724;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
}

.billing-registration .payment-status-partial {
  background-color: #fff3cd;
  color: #856404;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
}

.billing-registration .payment-status-pending {
  background-color: #f8d7da;
  color: #721c24;
  padding: 5px 10px;
  border-radius: 5px;
  font-weight: 600;
}

/* Age Input Styling */
.billing-registration .age-input-group {
  display: flex;
  gap: 10px;
  align-items: end;
}

.billing-registration .age-input-group .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* Title and Patient Type Styling */
.billing-registration .patient-info-row {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

/* Mother's Name Field for Baby/Infant */
.billing-registration .mother-name-section {
  background-color: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.billing-registration .mother-name-section .form-label {
  color: #0056b3;
  font-weight: 600;
}

/* Referral Source Styling */
.billing-registration .referral-section {
  background-color: #f0f8f0;
  border: 1px solid #c3e6c3;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

/* Enhanced Button Styling */
.billing-registration .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.billing-registration .btn-danger:hover:not(:disabled) {
  background: linear-gradient(45deg, #c82333, #a71e2a);
}

/* Compact Form Layout */
.billing-registration .compact-form .form-group {
  margin-bottom: 1rem;
}

.billing-registration .compact-form .form-label {
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.billing-registration .compact-form .form-control,
.billing-registration .compact-form .form-select {
  padding: 8px 12px;
  font-size: 0.9rem;
}

/* Print Styles */
@media print {
  .billing-registration .btn,
  .billing-registration .card-header {
    display: none !important;
  }

  .billing-registration .card {
    box-shadow: none;
    border: 1px solid #000;
  }

  .billing-registration {
    background-color: white;
  }

  .billing-registration .discount-section,
  .billing-registration .mother-name-section,
  .billing-registration .referral-section {
    background-color: white !important;
    border: 1px solid #000 !important;
  }
}
