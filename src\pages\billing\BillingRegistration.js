import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Card, Form, Button, Row, Col, Table, InputGroup, Alert, Modal, ListGroup } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft, faSave, faPlus, faTrash,
  faFileInvoiceDollar, faPrint, faEdit, faCheck, faSearch, faUser, faUserPlus,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { patientAPI } from '../../services/api';
import billingService from '../../services/billingAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import '../../styles/BillingRegistration.css';

// Enhanced Searchable Dropdown Component
const SearchableDropdown = ({
  options = [],
  value,
  onChange,
  placeholder = "Select...",
  name,
  label,
  isRequired = false,
  isDisabled = false,
  isClearable = true,
  getOptionLabel = (option) => option.label || option.name || option.description || option.test_profile || option,
  getOptionValue = (option) => option.value || option.id || option,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // Ensure options is always an array
  const safeOptions = Array.isArray(options) ? options : [];

  const filteredOptions = safeOptions.filter(option => {
    if (!option) return false;
    const label = getOptionLabel(option);
    return label && typeof label === 'string' &&
           label.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const selectedOption = safeOptions.find(option => getOptionValue(option) === value);

  return (
    <div className="searchable-dropdown">
      <Form.Group className="mb-3">
        {label && (
          <Form.Label>
            {label} {isRequired && <span className="text-danger">*</span>}
          </Form.Label>
        )}
        <div className="position-relative">
          {/* Hidden input for form validation */}
          <input
            type="hidden"
            name={name}
            value={value || ''}
            required={isRequired}
          />
          <Form.Control
            type="text"
            value={selectedOption ? getOptionLabel(selectedOption) : searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setIsOpen(true);
              // Clear selection if user is typing
              if (!selectedOption || e.target.value !== getOptionLabel(selectedOption)) {
                const event = {
                  target: {
                    name: name,
                    value: ''
                  }
                };
                onChange(event);
              }
            }}
            onFocus={() => setIsOpen(true)}
            onBlur={() => setTimeout(() => setIsOpen(false), 200)}
            placeholder={placeholder}
            disabled={isDisabled}
            className={isRequired && !value ? 'is-invalid' : ''}
          />
          {isOpen && filteredOptions.length > 0 && (
            <div className="dropdown-menu show position-absolute w-100" style={{ zIndex: 1050, maxHeight: '200px', overflowY: 'auto' }}>
              {filteredOptions.map((option, index) => (
                <button
                  key={index}
                  type="button"
                  className="dropdown-item"
                  onClick={() => {
                    const event = {
                      target: {
                        name: name,
                        value: getOptionValue(option)
                      }
                    };
                    onChange(event);
                    setSearchTerm('');
                    setIsOpen(false);
                  }}
                >
                  {getOptionLabel(option)}
                </button>
              ))}
            </div>
          )}
          {/* Clear button */}
          {isClearable && value && (
            <button
              type="button"
              className="btn btn-sm btn-outline-secondary position-absolute"
              style={{ right: '5px', top: '50%', transform: 'translateY(-50%)', zIndex: 10 }}
              onClick={() => {
                const event = {
                  target: {
                    name: name,
                    value: ''
                  }
                };
                onChange(event);
                setSearchTerm('');
              }}
            >
              ×
            </button>
          )}
        </div>
        {isRequired && !value && (
          <div className="invalid-feedback d-block">
            Please select a {label?.toLowerCase() || 'value'}.
          </div>
        )}
      </Form.Group>
    </div>
  );
};

const BillingRegistration = () => {
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { tenantData, accessibleTenants, currentTenantContext } = useTenant();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  // Bill management states
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentBillId, setCurrentBillId] = useState(null);
  const [billSaved, setBillSaved] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showInlinePatientAdd, setShowInlinePatientAdd] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalData, setApprovalData] = useState({
    approverName: '',
    approverSignature: '',
    approvalComments: '',
    approvalTimestamp: null
  });

  // Form data
  const [formData, setFormData] = useState({
    // Patient Details
    branch: '',
    date: new Date().toISOString().split('T')[0],
    no: '', // SID Number - will be auto-generated
    category: 'Normal',
    patient: '',
    title: '', // Title prefix (Mr., Mrs., etc.)
    patientName: '', // Full patient name
    dob: '',
    age: '',
    ageInput: '', // Direct age input (e.g., "24 years")
    ageUnit: 'Years',
    years: '',
    months: '',
    days: '',
    sex: 'Male',
    mobile: '',
    email: '',
    referrer: 'Doctor',
    referralSource: 'Doctor', // Comprehensive referral source
    source: '',
    patientType: 'Normal', // Normal, Baby/Infant
    motherName: '', // For baby/infant patients

    // Billing Details
    billAmount: 0.00,
    otherCharges: 0.00,
    discountType: 'percentage', // 'percentage' or 'amount'
    discountPercent: 0.00,
    discountAmount: 0.00,
    discountRemarks: '', // Mandatory when discount is applied
    gstAmount: 0.00,
    totalAmount: 0.00,
    amountPaid: 0.00,
    balanceToBePaid: 0.00,
    paymentStatus: 'pending', // pending, partial, paid
    paymentHistory: [], // Array to store payment history
    testResults: {}, // Object to store test results by test ID
    reportStatus: 'pending', // pending, approved, rejected
    approvalHistory: [], // Array to store approval history

    // Payment Details
    cash: '',
    bankName: '',
    referenceNumber: '',
    amount: '',

    // Additional Details
    remarks: '',
    emergency: false,
    deliveryMode: '',
    studyNo: '',
    subPeriod: '',
    subNo: '',

    // Clinical Details
    clinicalRemarks: '',

    // Test Items
    testItems: []
  });

  // Master data
  const [branches, setBranches] = useState([]);
  const [categories, setCategories] = useState([]);
  const [testProfiles, setTestProfiles] = useState([]);

  const [titleOptions] = useState([
    { id: 'Mr.', name: 'Mr.', gender: 'Male' },
    { id: 'Mrs.', name: 'Mrs.', gender: 'Female' },
    { id: 'Ms.', name: 'Ms.', gender: 'Female' },
    { id: 'Dr.', name: 'Dr.', gender: 'Any' },
    { id: 'Baby', name: 'Baby', gender: 'Any' },
    { id: 'Master.', name: 'Master.', gender: 'Male' },
    { id: 'Prof.', name: 'Prof.', gender: 'Any' },
    { id: 'Justice', name: 'Justice', gender: 'Any' },
    { id: 'Honourable', name: 'Honourable', gender: 'Any' },
    { id: 'Adv.', name: 'Adv.', gender: 'Any' },
    { id: 'Major', name: 'Major', gender: 'Any' },
    { id: 'Sister', name: 'Sister', gender: 'Female' },
    { id: 'Selvi.', name: 'Selvi.', gender: 'Female' },
    { id: 'COL', name: 'COL', gender: 'Any' },
    { id: 'Selvan.', name: 'Selvan.', gender: 'Male' }
  ]);
  const [referralSources] = useState([
    { id: 'Doctor', name: 'Doctor' },
    { id: 'Hospital', name: 'Hospital' },
    { id: 'Corporate', name: 'Corporate' },
    { id: 'Lab', name: 'Lab' },
    { id: 'Insurance', name: 'Insurance' },
    { id: 'Patient (Self)', name: 'Patient (Self)' }
  ]);

  // Test item form
  const [newTestItem, setNewTestItem] = useState({
    testName: '',
    test_id: null,
    amount: 0.00
  });



  // Patient search functionality
  const [showPatientSearch, setShowPatientSearch] = useState(false);
  const [patientSearchQuery, setPatientSearchQuery] = useState('');
  const [patientSearchResults, setPatientSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [selectedPatientData, setSelectedPatientData] = useState(null);

  // Patient add functionality
  const [showPatientAdd, setShowPatientAdd] = useState(false);
  const [showInlinePatientAdd, setShowInlinePatientAdd] = useState(false);
  const [patientAddData, setPatientAddData] = useState({
    title: '',
    first_name: '',
    last_name: '',
    gender: '',
    date_of_birth: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    state: 'Tamil Nadu',
    postal_code: '',
    emergency_contact: '',
    emergency_phone: '',
    blood_group: '',
    insurance_provider: '',
    insurance_id: '',
    tenant_id: null // Will be set based on user role
  });
  const [patientAddLoading, setPatientAddLoading] = useState(false);
  const [patientAddValidated, setPatientAddValidated] = useState(false);


  // Patient search functionality with debouncing for live search
  const handlePatientSearch = async (query = patientSearchQuery) => {
    // Ensure query is a string and handle edge cases
    const searchQuery = typeof query === 'string' ? query : (patientSearchQuery || '');

    if (!searchQuery.trim()) {
      setPatientSearchResults([]);
      return;
    }

    // Check if a branch is selected - if not, use current user's tenant
    let branchId = formData.branch;
    if (!branchId && currentUser?.tenant_id) {
      branchId = currentUser.tenant_id;
    }

    // Use default tenant if still no branch
    if (!branchId) {
      branchId = currentUser?.tenant_id || 2;
    }

    try {
      setSearchLoading(true);
      const response = await patientAPI.searchPatients(searchQuery, branchId);

      // Handle different response formats
      let patients = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          patients = response.data;
        } else if (response.data.items && Array.isArray(response.data.items)) {
          patients = response.data.items;
        } else if (typeof response.data === 'object') {
          patients = Object.values(response.data).filter(item => item && typeof item === 'object');
        }
      }

      setPatientSearchResults(patients);
    } catch (err) {
      console.error('Error searching patients:', err);
      setError('Failed to search patients. Please try again.');
      setPatientSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  // Debounced search effect for live search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const searchQuery = typeof patientSearchQuery === 'string' ? patientSearchQuery : '';
      if (searchQuery.trim().length >= 2) {
        handlePatientSearch();
      } else if (searchQuery.trim().length === 0) {
        setPatientSearchResults([]);
      }
    }, 300); // 300ms delay

    return () => clearTimeout(timeoutId);
  }, [patientSearchQuery, formData.branch]); // eslint-disable-line react-hooks/exhaustive-deps

  // Age calculation functions
  const calculateAgeFromDOB = (dob) => {
    if (!dob) return { years: '', months: '', days: '', ageInput: '' };

    const birthDate = new Date(dob);
    const today = new Date();

    let years = today.getFullYear() - birthDate.getFullYear();
    let months = today.getMonth() - birthDate.getMonth();
    let days = today.getDate() - birthDate.getDate();

    if (days < 0) {
      months--;
      days += new Date(today.getFullYear(), today.getMonth(), 0).getDate();
    }

    if (months < 0) {
      years--;
      months += 12;
    }

    // Format age input string
    let ageInput = '';
    if (years > 0) {
      ageInput = `${years} years`;
    } else if (months > 0) {
      ageInput = `${months} months`;
    } else {
      ageInput = `${days} days`;
    }

    return { years: years.toString(), months: months.toString(), days: days.toString(), ageInput };
  };

  const calculateDOBFromAge = (years, months, days) => {
    const today = new Date();
    const birthDate = new Date(today);

    birthDate.setFullYear(today.getFullYear() - (parseInt(years) || 0));
    birthDate.setMonth(today.getMonth() - (parseInt(months) || 0));
    birthDate.setDate(today.getDate() - (parseInt(days) || 0));

    return birthDate.toISOString().split('T')[0];
  };

  // Auto-select gender based on title
  const handleTitleChange = (title) => {
    const selectedTitle = titleOptions.find(t => t.id === title);
    if (selectedTitle && selectedTitle.gender !== 'Any') {
      setFormData(prev => ({
        ...prev,
        title,
        sex: selectedTitle.gender
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        title
      }));
    }
  };

  // Handle patient selection from search results
  const handlePatientSelect = (patient) => {
    setSelectedPatientData(patient);

    // Calculate current age from DOB
    const ageData = calculateAgeFromDOB(patient.date_of_birth);

    setFormData(prev => ({
      ...prev,
      patient: patient.id,
      patientName: `${patient.first_name} ${patient.last_name}`.toUpperCase(),
      dob: patient.date_of_birth || '',
      mobile: patient.phone || '',
      email: patient.email || '',
      sex: patient.gender || 'Male',
      ...ageData
    }));

    setShowPatientSearch(false);
    setPatientSearchQuery('');
    setPatientSearchResults([]);
  };

  // Handle patient add form changes
  const handlePatientAddChange = (e) => {
    const { name, value } = e.target;
    setPatientAddData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle patient add form submission
  const handlePatientAddSubmit = async (e) => {
    e.preventDefault();
    const form = e.currentTarget;

    if (form.checkValidity() === false) {
      e.stopPropagation();
      setPatientAddValidated(true);
      return;
    }

    setPatientAddValidated(true);
    setPatientAddLoading(true);
    setError(null);

    try {
      // Determine tenant_id based on user role
      let targetTenantId = null;

      if (currentUser?.role === 'admin' || currentUser?.role === 'hub_admin') {
        // For admin/hub_admin, use selected tenant from form or current context
        targetTenantId = patientAddData.tenant_id || currentTenantContext?.id || tenantData?.id;
      } else {
        // For other roles, use their own tenant
        targetTenantId = currentUser?.tenant_id;
      }

      const patientDataToSubmit = {
        ...patientAddData,
        tenant_id: targetTenantId
      };

      const response = await patientAPI.createPatient(patientDataToSubmit);

      // Select the newly created patient
      const newPatient = response.data;
      handlePatientSelect(newPatient);

      // Reset form and close modal/inline form
      setPatientAddData({
        title: '',
        first_name: '',
        last_name: '',
        gender: '',
        date_of_birth: '',
        phone: '',
        email: '',
        address: '',
        city: '',
        state: 'Tamil Nadu',
        postal_code: '',
        emergency_contact: '',
        emergency_phone: '',
        blood_group: '',
        insurance_provider: '',
        insurance_id: '',
        tenant_id: null
      });
      setPatientAddValidated(false);
      setShowPatientAdd(false);
      setShowInlinePatientAdd(false);
      setSuccess('Patient created and selected successfully!');

    } catch (err) {
      console.error('Error creating patient:', err);
      setError(err.response?.data?.message || 'Failed to create patient. Please try again.');
    } finally {
      setPatientAddLoading(false);
    }
  };

  // Initialize patient add form when modal opens
  const handlePatientAddOpen = () => {
    // Set default tenant_id for admin/hub_admin roles
    if (currentUser?.role === 'admin' || currentUser?.role === 'hub_admin') {
      setPatientAddData(prev => ({
        ...prev,
        tenant_id: formData.branch || currentTenantContext?.id || tenantData?.id
      }));
    }
    setShowPatientAdd(true);
  };

  // Load existing bill data for editing
  const loadBillData = async (billId) => {
    try {
      setLoading(true);
      const response = await billingService.getBillingById(billId);
      const billData = response.data;

      // Populate form with existing bill data
      setFormData({
        ...billData,
        testItems: billData.items || [],
        patient: billData.patient_id,
        // Map other fields as needed
      });

      // Load patient data
      if (billData.patient_id) {
        const patientResponse = await patientAPI.getPatientById(billData.patient_id);
        setSelectedPatientData(patientResponse.data);
      }

      setIsEditMode(true);
      setCurrentBillId(billId);
      setBillSaved(true);
    } catch (err) {
      console.error('Error loading bill data:', err);
      setError('Failed to load bill data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle bill deletion
  const handleDeleteBill = async () => {
    try {
      setLoading(true);
      await billingService.deleteBilling(currentBillId);
      setSuccess('Bill deleted successfully!');

      // Redirect to billing list after successful deletion
      setTimeout(() => {
        navigate('/billing');
      }, 2000);
    } catch (err) {
      console.error('Error deleting bill:', err);
      setError('Failed to delete bill. Please try again.');
    } finally {
      setLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  // Handle bill update (for edit mode)
  const handleUpdateBill = async (billingData) => {
    try {
      setLoading(true);
      const response = await billingService.updateBilling(currentBillId, billingData);

      if (response.data) {
        setSuccess('Bill updated successfully!');
        setBillSaved(true);

        // Update current bill data
        setFormData(prev => ({
          ...prev,
          ...response.data
        }));
      }
    } catch (err) {
      console.error('Error updating bill:', err);
      setError('Failed to update bill. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle test result changes
  const handleTestResultChange = (testId, field, value) => {
    setFormData(prev => ({
      ...prev,
      testResults: {
        ...prev.testResults,
        [testId]: {
          ...prev.testResults[testId],
          [field]: value
        }
      }
    }));
  };

  // Check if a value is within the reference range
  const isValueInRange = (value, referenceRange) => {
    if (!value || !referenceRange) return true;

    // Simple range checking - can be enhanced for complex ranges
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return true;

    // Extract numeric ranges like "10-50" or "< 100" or "> 5"
    const rangeMatch = referenceRange.match(/(\d+(?:\.\d+)?)\s*-\s*(\d+(?:\.\d+)?)/);
    if (rangeMatch) {
      const min = parseFloat(rangeMatch[1]);
      const max = parseFloat(rangeMatch[2]);
      return numValue >= min && numValue <= max;
    }

    const lessThanMatch = referenceRange.match(/<\s*(\d+(?:\.\d+)?)/);
    if (lessThanMatch) {
      const max = parseFloat(lessThanMatch[1]);
      return numValue < max;
    }

    const greaterThanMatch = referenceRange.match(/>\s*(\d+(?:\.\d+)?)/);
    if (greaterThanMatch) {
      const min = parseFloat(greaterThanMatch[1]);
      return numValue > min;
    }

    return true; // Default to normal if can't parse range
  };

  // Check if all tests are ready for approval
  const areAllTestsReadyForApproval = () => {
    if (!formData.testItems.length) return false;

    return formData.testItems.every(item => {
      const testResult = formData.testResults?.[item.id];
      return testResult && testResult.result && testResult.status !== 'pending';
    });
  };

  // Handle approval submission
  const handleApprovalSubmit = async () => {
    try {
      setLoading(true);

      const approvalRecord = {
        ...approvalData,
        approvalTimestamp: new Date().toISOString(),
        billId: currentBillId,
        testResults: formData.testResults
      };

      // Here you would typically save the approval to your backend
      console.log('Approval submitted:', approvalRecord);

      // Update form data with approval
      setFormData(prev => ({
        ...prev,
        approvalHistory: [...(prev.approvalHistory || []), approvalRecord],
        reportStatus: 'approved'
      }));

      setShowApprovalModal(false);
      setSuccess('Report approved successfully!');

    } catch (err) {
      console.error('Error submitting approval:', err);
      setError('Failed to submit approval. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Generate SID number based on franchise
  const generateSIDNumber = async (branchId = null) => {
    try {
      // Use the provided branchId or fall back to current context
      const targetTenantId = branchId || currentTenantContext?.id || tenantData?.id;

      const response = await fetch('/api/billing/generate-sid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          tenant_id: targetTenantId,
          user_role: currentUser?.role
        })
      });

      if (response.ok) {
        const data = await response.json();
        return data.sid_number;
      } else {
        throw new Error('Failed to generate SID');
      }
    } catch (err) {
      console.error('Error generating SID:', err);
      // Proper error handling - no fallback SID generation
      const selectedBranch = branches.find(b => b.id.toString() === (branchId || formData.branch));
      const siteCode = selectedBranch?.site_code || currentTenantContext?.site_code || tenantData?.site_code;

      if (!siteCode) {
        throw new Error('Unable to determine franchise site code for SID generation. Please contact system administrator.');
      }

      throw new Error(`Failed to generate SID for franchise ${siteCode}. Please try again or contact system administrator.`);
    }
  };

  // Get branches based on user role and franchise access
  const getBranchesForUser = () => {
    if (!currentUser || !tenantData) return [];

    // For Mayiladuthurai (Hub Admin) and Admin roles: show ALL available franchises/branches
    if (currentUser.role === 'admin' || currentUser.role === 'hub_admin') {
      // Check if user is from Mayiladuthurai hub (can see all franchises)
      if (tenantData.is_hub || currentUser.role === 'admin') {
        return accessibleTenants || [];
      }
    }

    // For all other franchise roles: show only their specific assigned franchise
    // This includes franchise_admin and any other non-admin roles
    if (currentUser.role === 'franchise_admin' || currentUser.role !== 'admin') {
      // Use accessibleTenants if available (should contain their own franchise)
      if (accessibleTenants && accessibleTenants.length > 0) {
        return accessibleTenants;
      }
      // Fallback to tenantData (their own franchise only)
      return [tenantData];
    }

    // Default fallback for other roles
    return [tenantData];
  };

  // Fetch master data
  useEffect(() => {
    const fetchMasterData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Set branches based on user access control
        const userBranches = getBranchesForUser();
        setBranches(userBranches);

        // Auto-select branch for non-admin users who have only one franchise
        if ((currentUser?.role === 'franchise_admin' ||
             (currentUser?.role !== 'admin' && currentUser?.role !== 'hub_admin')) &&
            userBranches.length === 1 && !formData.branch) {
          setFormData(prev => ({
            ...prev,
            branch: userBranches[0].id.toString()
          }));
        }

        setCategories([
          { id: 'Normal', name: 'Normal' },
          { id: 'Emergency', name: 'Emergency' },
          { id: 'VIP', name: 'VIP' }
        ]);



        // Set sample test profiles with proper structure
        const sampleTestProfiles = [
          { id: 1, test_profile: 'Complete Blood Count (CBC)', test_price: 250, department: 'Hematology' },
          { id: 2, test_profile: 'Lipid Profile', test_price: 400, department: 'Biochemistry' },
          { id: 3, test_profile: 'Liver Function Test (LFT)', test_price: 350, department: 'Biochemistry' },
          { id: 4, test_profile: 'Kidney Function Test (KFT)', test_price: 300, department: 'Biochemistry' },
          { id: 5, test_profile: 'Thyroid Profile (T3, T4, TSH)', test_price: 500, department: 'Endocrinology' },
          { id: 6, test_profile: 'Blood Sugar (Fasting)', test_price: 100, department: 'Biochemistry' },
          { id: 7, test_profile: 'Blood Sugar (Random)', test_price: 100, department: 'Biochemistry' },
          { id: 8, test_profile: 'HbA1c', test_price: 450, department: 'Biochemistry' },
          { id: 9, test_profile: 'Urine Routine', test_price: 150, department: 'Pathology' },
          { id: 10, test_profile: 'ECG', test_price: 200, department: 'Cardiology' }
        ];
        setTestProfiles(sampleTestProfiles);

        // Sample patients are now handled through search functionality

        // Try to fetch test master data from billing API
        try {
          const testMasterResponse = await fetch('/api/billing/test-master', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          });

          if (testMasterResponse.ok) {
            const testMasterData = await testMasterResponse.json();
            if (testMasterData.success && testMasterData.data) {
              const testProfilesFromAPI = testMasterData.data.map(test => ({
                id: test.id,
                testName: test.testName,
                test_profile: test.testName || test.displayName,
                test_price: test.testPrice || 0,
                department: test.department || 'General',
                hmsCode: test.hmsCode || '',
                specimen: test.specimen || '',
                container: test.container || '',
                serviceTime: test.serviceTime || '',
                reportingDays: test.reportingDays || '',
                cutoffTime: test.cutoffTime || '',
                ...test
              }));
              setTestProfiles(testProfilesFromAPI);
              console.log(`Loaded ${testProfilesFromAPI.length} tests from test_master API`);
            }
          } else {
            console.error('Failed to fetch test master data from billing API');
            setError('Unable to load test profiles. Please contact system administrator.');
            return;
          }
        } catch (apiErr) {
          console.error('Error fetching test master data:', apiErr);
          setError('Unable to load test profiles. Please contact system administrator.');
          return;
        }



        // Patient data is now fetched through search functionality when needed

      } catch (err) {
        console.error('Error fetching master data:', err);
        setError('Failed to load master data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchMasterData();
  }, [currentUser, tenantData, accessibleTenants]); // eslint-disable-line react-hooks/exhaustive-deps

  // Generate SID number when component loads or branch changes
  useEffect(() => {
    const initializeSID = async () => {
      if (formData.branch) {
        // Clear existing SID when branch changes and generate new one
        const sidNumber = await generateSIDNumber(formData.branch);
        setFormData(prev => ({
          ...prev,
          no: sidNumber
        }));
      } else {
        // Clear SID if no branch is selected
        setFormData(prev => ({
          ...prev,
          no: ''
        }));
      }
    };

    initializeSID();
  }, [formData.branch]); // eslint-disable-line react-hooks/exhaustive-deps

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === 'checkbox') {
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }));
      return;
    }

    // Handle special cases
    if (name === 'title') {
      handleTitleChange(value);
      return;
    }

    if (name === 'dob') {
      const ageData = calculateAgeFromDOB(value);
      setFormData(prev => ({
        ...prev,
        dob: value,
        ...ageData
      }));
      return;
    }

    if (name === 'years' || name === 'months' || name === 'days') {
      const updatedFormData = { ...formData, [name]: value };
      const dob = calculateDOBFromAge(
        updatedFormData.years || '0',
        updatedFormData.months || '0',
        updatedFormData.days || '0'
      );
      setFormData(prev => ({
        ...prev,
        [name]: value,
        dob: dob
      }));
      return;
    }

    // Handle discount remarks auto-enable
    if (name === 'discountRemarks' && value.trim()) {
      setFormData(prev => ({
        ...prev,
        [name]: value,
        discountPercent: prev.discountPercent || 5 // Auto-enable with 5% default
      }));
      return;
    }

    // Convert patient name to uppercase
    if (name === 'patientName') {
      setFormData(prev => ({
        ...prev,
        [name]: value.toUpperCase()
      }));
      return;
    }

    // Default case
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle test item changes
  const handleTestItemChange = (e) => {
    const { name, value } = e.target;
    setNewTestItem(prev => {
      const updated = { ...prev, [name]: value };

      // If test name is selected, auto-fill amount and test_id
      if (name === 'testName') {
        const selectedProfile = testProfiles.find(profile => profile.id === value);
        if (selectedProfile) {
          updated.amount = parseFloat(selectedProfile.test_price) || 0;
          updated.test_id = selectedProfile.id;
        }
      }

      return updated;
    });
  };

  // Add test item
  const addTestItem = () => {
    // Clear any previous errors
    setError(null);

    // Validate test selection
    if (!newTestItem.testName || !newTestItem.test_id) {
      setError('Please select a test name');
      return;
    }

    // Allow amount to be 0 or greater (users can enter amounts manually)
    if (newTestItem.amount === '' || newTestItem.amount === null || newTestItem.amount === undefined) {
      setError('Please enter an amount (0 or greater)');
      return;
    }

    const amount = parseFloat(newTestItem.amount);
    if (isNaN(amount) || amount < 0) {
      setError('Please enter a valid amount (0 or greater)');
      return;
    }

    // Check if test is already added (check by test_id for accuracy)
    const selectedTestProfile = testProfiles.find(profile => profile.id === newTestItem.testName);
    const existingTest = formData.testItems.find(item => item.test_id === newTestItem.test_id);

    if (existingTest) {
      setError('This test has already been added');
      return;
    }

    // Get the selected test profile details
    const selectedProfile = selectedTestProfile;
    const testItemToAdd = {
      ...newTestItem,
      id: Date.now(),
      test_id: newTestItem.test_id,
      testName: selectedProfile ? (selectedProfile.testName || selectedProfile.test_profile) : newTestItem.testName,
      test_name: selectedProfile ? (selectedProfile.testName || selectedProfile.test_profile) : newTestItem.testName, // For backend compatibility
      department: selectedProfile ? selectedProfile.department : 'General',
      hmsCode: selectedProfile ? selectedProfile.hmsCode : '',
      amount: parseFloat(newTestItem.amount) || 0
    };

    setFormData(prev => ({
      ...prev,
      testItems: [...prev.testItems, testItemToAdd]
    }));

    // Reset test item form
    setNewTestItem({
      testName: '',
      test_id: null,
      amount: 0.00
    });
  };

  // Remove test item
  const removeTestItem = (id) => {
    setFormData(prev => ({
      ...prev,
      testItems: prev.testItems.filter(item => item.id !== id)
    }));
  };

  // Calculate totals (GST removed as per requirement)
  const calculateTotals = () => {
    setFormData(prev => {
      const billAmount = prev.testItems.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
      const otherCharges = parseFloat(prev.otherCharges) || 0;

      // Calculate discount based on type
      let discountAmount = 0;
      if (prev.discountType === 'percentage') {
        discountAmount = (billAmount * parseFloat(prev.discountPercent || 0)) / 100;
      } else {
        discountAmount = parseFloat(prev.discountAmount || 0);
      }

      // Calculate total amount (no GST as per requirement)
      const totalAmount = billAmount + otherCharges - discountAmount;

      const amountPaid = parseFloat(prev.amountPaid) || 0;
      const balanceToBePaid = totalAmount - amountPaid;

      // Determine payment status
      let paymentStatus = 'pending';
      if (amountPaid >= totalAmount) {
        paymentStatus = 'paid';
      } else if (amountPaid > 0) {
        paymentStatus = 'partial';
      }

      return {
        ...prev,
        billAmount: billAmount.toFixed(2),
        totalAmount: totalAmount.toFixed(2),
        balanceToBePaid: balanceToBePaid.toFixed(2),
        paymentStatus
      };
    });
  };

  // Recalculate totals when relevant fields change
  useEffect(() => {
    calculateTotals();
  }, [formData.testItems, formData.otherCharges, formData.discountType, formData.discountPercent, formData.discountAmount, formData.amountPaid]);

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    setError(null);

    // Validate required fields
    if (!formData.branch) {
      setError('Please select a branch');
      return;
    }

    if (!formData.patient) {
      setError('Please select a patient');
      return;
    }

    if (formData.testItems.length === 0) {
      setError('Please add at least one test');
      return;
    }

    // Validate discount remarks if discount is applied
    if ((formData.discountPercent > 0 || formData.discountAmount > 0) && !formData.discountRemarks.trim()) {
      setError('Discount remarks are required when applying discount');
      return;
    }

    // Validate mother's name for baby/infant patients
    if (formData.patientType === 'Baby/Infant' && !formData.motherName.trim()) {
      setError('Mother\'s name is required for baby/infant patients');
      return;
    }

    // Validate payment for report printing (if payment status is not paid, disable report printing)
    if (formData.paymentStatus !== 'paid' && formData.printBill) {
      setError('Full payment is required before printing reports');
      return;
    }

    // Validate that all test items have valid data
    const invalidTestItems = formData.testItems.filter(item => {
      if (!item.testName || !item.test_id) return true; // Test name and ID are required
      const amount = parseFloat(item.amount);
      return isNaN(amount) || amount < 0; // Amount must be 0 or greater
    });
    if (invalidTestItems.length > 0) {
      setError('All test items must have a valid test name and amount (0 or greater)');
      return;
    }

    try {
      setLoading(true);

      const billingData = {
        ...formData,
        patient_id: formData.patient, // Map patient to patient_id
        items: formData.testItems,
        total_amount: parseFloat(formData.totalAmount),
        paid_amount: parseFloat(formData.amountPaid),
        balance: parseFloat(formData.balanceToBePaid),
        bill_amount: parseFloat(formData.billAmount),
        other_charges: parseFloat(formData.otherCharges),
        discount_type: formData.discountType,
        discount_percent: parseFloat(formData.discountPercent),
        discount_amount: parseFloat(formData.discountAmount),
        discount_remarks: formData.discountRemarks,
        payment_status: formData.paymentStatus,
        patient_type: formData.patientType,
        mother_name: formData.motherName,
        referral_source: formData.referralSource
      };

      const response = await billingService.createBilling(billingData);

      if (response.success) {
        setSuccess(true);
        setBillSaved(true);
        setCurrentBillId(response.data.id);
        setTimeout(() => {
          navigate(`/billing/${response.data.id}`);
        }, 2000);
      } else {
        setError(response.error || 'Failed to create billing record. Please try again.');
      }

    } catch (err) {
      console.error('Error creating billing:', err);
      setError(err.response?.data?.message || 'Failed to create billing record. Please try again.');
    } finally {
      setLoading(false);
    }
  };



  return (
    <div className="billing-registration">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 className="h3 mb-0 text-primary">
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
            REGISTRATION / BILLING - ADD
          </h1>
        </div>
        <div>
          <Link to="/billing" className="btn btn-secondary me-2">
            <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
            Back to Dashboard
          </Link>
          <Button variant="success" onClick={handleSubmit} disabled={loading}>
            <FontAwesomeIcon icon={faSave} className="me-2" />
            {loading ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <Alert variant="success" className="mb-4">
          <FontAwesomeIcon icon={faCheck} className="me-2" />
          Billing record created successfully! Redirecting...
        </Alert>
      )}
      
      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        <Row>
          {/* Left Column */}
          <Col lg={8}>
            {/* Patient Information */}
            <Card className="shadow mb-4">
              <Card.Header className="bg-primary text-white">
                <h6 className="mb-0">Patient Information</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>Branch</Form.Label>
                      <Form.Select
                        name="branch"
                        value={formData.branch}
                        onChange={handleChange}
                        required
                      >
                        <option value="">Select Branch</option>
                        {branches.map(branch => (
                          <option key={branch.id} value={branch.id}>
                            {branch.name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>Date</Form.Label>
                      <Form.Control
                        type="date"
                        name="date"
                        value={formData.date}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>SID No.</Form.Label>
                      <Form.Control
                        type="text"
                        name="no"
                        value={formData.no}
                        onChange={handleChange}
                        placeholder="Auto-generated (e.g., MYD001)"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>Category</Form.Label>
                      <Form.Select
                        name="category"
                        value={formData.category}
                        onChange={handleChange}
                      >
                        {categories.map(category => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                </Row>

                {/* Enhanced Patient Search Section */}
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Patient Search & Selection</Form.Label>
                      <div className="d-flex gap-2">
                        <InputGroup>
                          <InputGroup.Text>
                            <FontAwesomeIcon icon={faSearch} />
                          </InputGroup.Text>
                          <Form.Control
                            type="text"
                            placeholder="Type to search patients by ID, Name, or Mobile Number..."
                            value={patientSearchQuery}
                            onChange={(e) => setPatientSearchQuery(e.target.value)}
                          />
                          {searchLoading && (
                            <InputGroup.Text>
                              <div className="spinner-border spinner-border-sm" role="status">
                                <span className="visually-hidden">Searching...</span>
                              </div>
                            </InputGroup.Text>
                          )}
                        </InputGroup>
                        <Button
                          variant="success"
                          onClick={() => setShowPatientSearch(true)}
                          title="Advanced Patient Search"
                        >
                          <FontAwesomeIcon icon={faUser} />
                        </Button>
                        <Button
                          variant="primary"
                          onClick={() => setShowInlinePatientAdd(!showInlinePatientAdd)}
                          title={showInlinePatientAdd ? "Hide Add Patient Form" : "Add New Patient"}
                        >
                          <FontAwesomeIcon icon={showInlinePatientAdd ? faArrowLeft : faUserPlus} />
                        </Button>
                      </div>

                      {/* Selected Patient Display */}
                      {selectedPatientData && (
                        <div className="mt-2 p-2 border rounded">
                          <small className="text-muted">Selected Patient:</small>
                          <div className="fw-bold">
                            {selectedPatientData.first_name} {selectedPatientData.last_name}
                            <span className="text-muted ms-2">
                              (ID: {selectedPatientData.patient_id || selectedPatientData.id})
                            </span>
                            <span className="text-muted ms-2">
                              Mobile: {selectedPatientData.phone}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Quick Search Results */}
                      {(patientSearchResults && patientSearchResults.length > 0) && (
                        <div className="mt-2 border rounded" style={{ maxHeight: '200px', overflowY: 'auto' }}>
                          <div className="p-2  border-bottom">
                            <small className="text-muted">Found {patientSearchResults.length} patients</small>
                          </div>
                          {patientSearchResults.map((patient) => (
                            <div
                              key={patient.id}
                              className="p-2 border-bottom cursor-pointer hover-bg-light"
                              onClick={() => handlePatientSelect(patient)}
                              style={{ cursor: 'pointer' }}
                              onMouseEnter={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                              onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
                            >
                              <div className="fw-bold">
                                {patient.first_name} {patient.last_name}
                              </div>
                              <small className="text-muted">
                                ID: {patient.patient_id || patient.id} | Mobile: {patient.phone} | Tenant: {patient.tenant_id}
                              </small>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* No Results Fallback for Quick Search */}
                      {patientSearchQuery && patientSearchQuery.trim().length >= 2 &&
                       patientSearchResults && patientSearchResults.length === 0 && !searchLoading && (
                        <div className="mt-2 p-3 border rounded bg-light text-center">
                          <div className="text-muted mb-2">
                            <small>No patients found for "{patientSearchQuery}"</small>
                          </div>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={() => {
                              // Pre-fill patient add form with search query
                              const searchTerms = patientSearchQuery.trim().split(' ');
                              if (searchTerms.length >= 2) {
                                setPatientAddData(prev => ({
                                  ...prev,
                                  first_name: searchTerms[0],
                                  last_name: searchTerms.slice(1).join(' ')
                                }));
                              } else if (searchTerms.length === 1) {
                                // Check if it's a phone number (all digits)
                                if (/^\d+$/.test(searchTerms[0])) {
                                  setPatientAddData(prev => ({
                                    ...prev,
                                    phone: searchTerms[0]
                                  }));
                                } else {
                                  setPatientAddData(prev => ({
                                    ...prev,
                                    first_name: searchTerms[0]
                                  }));
                                }
                              }
                              setShowInlinePatientAdd(true);
                              setPatientSearchQuery('');
                            }}
                          >
                            <FontAwesomeIcon icon={faUserPlus} className="me-1" />
                            Add New Patient
                          </Button>
                        </div>
                      )}

                      {/* Inline Add New Patient Section */}
                      {showInlinePatientAdd && (
                        <div className="mt-3 p-3 border rounded bg-light">
                          <div className="d-flex justify-content-between align-items-center mb-3">
                            <h6 className="mb-0">
                              <FontAwesomeIcon icon={faUserPlus} className="me-2" />
                              Add New Patient
                            </h6>
                            <Button
                              variant="outline-secondary"
                              size="sm"
                              onClick={() => {
                                setShowInlinePatientAdd(false);
                                setPatientAddData({
                                  title: '',
                                  first_name: '',
                                  last_name: '',
                                  gender: '',
                                  date_of_birth: '',
                                  phone: '',
                                  email: '',
                                  address: '',
                                  city: '',
                                  state: '',
                                  postal_code: '',
                                  emergency_contact: '',
                                  emergency_phone: '',
                                  blood_group: '',
                                  insurance_provider: '',
                                  insurance_id: '',
                                  tenant_id: ''
                                });
                              }}
                            >
                              <FontAwesomeIcon icon={faArrowLeft} className="me-1" />
                              Cancel
                            </Button>
                          </div>

                          <Form onSubmit={handlePatientAddSubmit}>
                            {/* Essential Patient Information */}
                            <Row>
                              <Col md={2}>
                                <SearchableDropdown
                                  name="title"
                                  label="Title"
                                  value={patientAddData.title}
                                  onChange={(e) => setPatientAddData(prev => ({ ...prev, title: e.target.value }))}
                                  options={titleOptions}
                                  placeholder="Select title..."
                                  getOptionLabel={(option) => option.name}
                                  getOptionValue={(option) => option.id}
                                />
                              </Col>
                              <Col md={5}>
                                <Form.Group className="mb-3">
                                  <Form.Label>First Name <span className="text-danger">*</span></Form.Label>
                                  <Form.Control
                                    type="text"
                                    name="first_name"
                                    value={patientAddData.first_name}
                                    onChange={handlePatientAddChange}
                                    required
                                    placeholder="Enter first name"
                                  />
                                </Form.Group>
                              </Col>
                              <Col md={5}>
                                <Form.Group className="mb-3">
                                  <Form.Label>Last Name <span className="text-danger">*</span></Form.Label>
                                  <Form.Control
                                    type="text"
                                    name="last_name"
                                    value={patientAddData.last_name}
                                    onChange={handlePatientAddChange}
                                    required
                                    placeholder="Enter last name"
                                  />
                                </Form.Group>
                              </Col>
                            </Row>

                            <Row>
                              <Col md={3}>
                                <Form.Group className="mb-3">
                                  <Form.Label>Date of Birth <span className="text-danger">*</span></Form.Label>
                                  <Form.Control
                                    type="date"
                                    name="date_of_birth"
                                    value={patientAddData.date_of_birth}
                                    onChange={handlePatientAddChange}
                                    required
                                  />
                                </Form.Group>
                              </Col>
                              <Col md={3}>
                                <Form.Group className="mb-3">
                                  <Form.Label>Gender <span className="text-danger">*</span></Form.Label>
                                  <Form.Select
                                    name="gender"
                                    value={patientAddData.gender}
                                    onChange={handlePatientAddChange}
                                    required
                                  >
                                    <option value="">Select Gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                  </Form.Select>
                                </Form.Group>
                              </Col>
                              <Col md={3}>
                                <Form.Group className="mb-3">
                                  <Form.Label>Mobile <span className="text-danger">*</span></Form.Label>
                                  <Form.Control
                                    type="tel"
                                    name="phone"
                                    value={patientAddData.phone}
                                    onChange={handlePatientAddChange}
                                    required
                                    placeholder="Enter mobile number"
                                  />
                                </Form.Group>
                              </Col>
                              <Col md={3}>
                                <Form.Group className="mb-3">
                                  <Form.Label>Email</Form.Label>
                                  <Form.Control
                                    type="email"
                                    name="email"
                                    value={patientAddData.email}
                                    onChange={handlePatientAddChange}
                                    placeholder="Enter email address"
                                  />
                                </Form.Group>
                              </Col>
                            </Row>

                            <Row>
                              <Col md={12}>
                                <Form.Group className="mb-3">
                                  <Form.Label>Address</Form.Label>
                                  <Form.Control
                                    as="textarea"
                                    rows={2}
                                    name="address"
                                    value={patientAddData.address}
                                    onChange={handlePatientAddChange}
                                    placeholder="Enter complete address"
                                  />
                                </Form.Group>
                              </Col>
                            </Row>

                            <div className="d-flex justify-content-end gap-2">
                              <Button
                                variant="secondary"
                                type="button"
                                onClick={() => {
                                  setShowInlinePatientAdd(false);
                                  setPatientAddData({
                                    title: '',
                                    first_name: '',
                                    last_name: '',
                                    gender: '',
                                    date_of_birth: '',
                                    phone: '',
                                    email: '',
                                    address: '',
                                    city: '',
                                    state: '',
                                    postal_code: '',
                                    emergency_contact: '',
                                    emergency_phone: '',
                                    blood_group: '',
                                    insurance_provider: '',
                                    insurance_id: '',
                                    tenant_id: ''
                                  });
                                }}
                              >
                                Cancel
                              </Button>
                              <Button variant="success" type="submit" disabled={loading}>
                                <FontAwesomeIcon icon={faSave} className="me-2" />
                                {loading ? 'Creating...' : 'Create Patient'}
                              </Button>
                            </div>
                          </Form>
                        </div>
                      )}


                    </Form.Group>
                  </Col>
                </Row>

                {/* Title and Patient Name Fields */}
                <Row>
                  <Col md={2}>
                    <SearchableDropdown
                      name="title"
                      label="Title"
                      value={formData.title}
                      onChange={handleChange}
                      options={titleOptions}
                      placeholder="Select title..."
                      getOptionLabel={(option) => option.name}
                      getOptionValue={(option) => option.id}
                    />
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Patient Name</Form.Label>
                      <Form.Control
                        type="text"
                        name="patientName"
                        value={formData.patientName || (selectedPatientData ? `${selectedPatientData.first_name} ${selectedPatientData.last_name}`.toUpperCase() : '')}
                        onChange={handleChange}
                        placeholder="Patient name (auto-converted to CAPS)"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>Patient ID</Form.Label>
                      <Form.Control
                        type="text"
                        name="patientId"
                        value={selectedPatientData ? selectedPatientData.patient_id : ''}
                        placeholder="Patient ID will appear here after selection"
                        readOnly
                        className=""
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <SearchableDropdown
                      name="patientType"
                      label="Patient Type"
                      value={formData.patientType}
                      onChange={handleChange}
                      options={[
                        { id: 'Normal', name: 'Normal' },
                        { id: 'Baby/Infant', name: 'Baby/Infant' }
                      ]}
                      placeholder="Select type..."
                      getOptionLabel={(option) => option.name}
                      getOptionValue={(option) => option.id}
                    />
                  </Col>
                </Row>

                {/* Mother's Name for Baby/Infant */}
                {formData.patientType === 'Baby/Infant' && (
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Mother's Name <span className="text-danger">*</span></Form.Label>
                        <Form.Control
                          type="text"
                          name="motherName"
                          value={formData.motherName}
                          onChange={handleChange}
                          placeholder="Enter mother's name"
                          required={formData.patientType === 'Baby/Infant'}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                )}

                {/* DOB and Age Management */}
                <Row>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>Date of Birth</Form.Label>
                      <Form.Control
                        type="date"
                        name="dob"
                        value={formData.dob}
                        onChange={handleChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>OR Direct Age Input</Form.Label>
                      <Form.Control
                        type="text"
                        name="ageInput"
                        value={formData.ageInput}
                        onChange={handleChange}
                        placeholder="e.g., 24 years, 6 months"
                        readOnly
                      />
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group className="mb-3">
                      <Form.Label>Years</Form.Label>
                      <Form.Control
                        type="number"
                        name="years"
                        value={formData.years}
                        onChange={handleChange}
                        placeholder="Years"
                        min="0"
                        max="150"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group className="mb-3">
                      <Form.Label>Months</Form.Label>
                      <Form.Control
                        type="number"
                        name="months"
                        value={formData.months}
                        onChange={handleChange}
                        placeholder="Months"
                        min="0"
                        max="11"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={2}>
                    <Form.Group className="mb-3">
                      <Form.Label>Days</Form.Label>
                      <Form.Control
                        type="number"
                        name="days"
                        value={formData.days}
                        onChange={handleChange}
                        placeholder="Days"
                        min="0"
                        max="30"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={2}>
                    <Form.Group className="mb-3">
                      <Form.Label>Sex</Form.Label>
                      <Form.Select
                        name="sex"
                        value={formData.sex}
                        onChange={handleChange}
                      >
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <Form.Group className="mb-3">
                      <Form.Label>Mobile</Form.Label>
                      <Form.Control
                        type="tel"
                        name="mobile"
                        value={formData.mobile}
                        onChange={handleChange}
                        placeholder="Mobile number"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Email</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        placeholder="Email address"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={3}>
                    <SearchableDropdown
                      name="referralSource"
                      label="Referral Source"
                      value={formData.referralSource}
                      onChange={handleChange}
                      options={referralSources}
                      placeholder="Select referral source..."
                      getOptionLabel={(option) => option.name}
                      getOptionValue={(option) => option.id}
                      isRequired={true}
                    />
                  </Col>
                </Row>

                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Source</Form.Label>
                      <Form.Control
                        type="text"
                        name="source"
                        value={formData.source}
                        onChange={handleChange}
                        placeholder="Source information"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Test Selection */}
            <Card className="shadow mb-4">
              <Card.Header className="bg-info text-white">
                <h6 className="mb-0">Select Test / Profile</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={8}>
                    <SearchableDropdown
                      name="testName"
                      label="Test Name"
                      value={newTestItem.testName}
                      onChange={handleTestItemChange}
                      options={testProfiles}
                      placeholder="Search and select test name..."
                      getOptionLabel={(option) => option.testName || option.test_profile || option.name || 'Unknown Test'}
                      getOptionValue={(option) => option.id}
                      isRequired={true}
                    />
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Amount</Form.Label>
                      <InputGroup>
                        <InputGroup.Text>₹</InputGroup.Text>
                        <Form.Control
                          type="number"
                          name="amount"
                          value={newTestItem.amount}
                          onChange={handleTestItemChange}
                          step="0.01"
                          min="0"
                        />
                      </InputGroup>
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex justify-content-end mb-3">
                  <Button
                    variant="primary"
                    onClick={addTestItem}
                    disabled={!newTestItem.testName}
                  >
                    <FontAwesomeIcon icon={faPlus} className="me-2" />
                    Add Test
                  </Button>
                </div>

                {/* Selected Tests Table */}
                {formData.testItems.length > 0 && (
                  <div className="table-responsive">
                    <Table striped bordered hover>
                      <thead className="table-dark">
                        <tr>
                          <th>Test ID</th>
                          <th>Test Name</th>
                          <th>Amount</th>
                          <th>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.testItems.map((item) => (
                          <tr key={item.id}>
                            <td>
                              <span className="badge bg-primary">
                                {item.test_id || 'N/A'}
                              </span>
                            </td>
                            <td>
                              <div>
                                <strong>{item.testName}</strong>
                                {item.department && (
                                  <div>
                                    <small className="text-muted">{item.department}</small>
                                  </div>
                                )}
                              </div>
                            </td>
                            <td>₹{parseFloat(item.amount).toFixed(2)}</td>
                            <td>
                              <Button
                                variant="danger"
                                size="sm"
                                onClick={() => removeTestItem(item.id)}
                              >
                                <FontAwesomeIcon icon={faTrash} />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="table-info">
                          <th colSpan="2">Total Test(s): {formData.testItems.length}</th>
                          <th>Total Amount: ₹{formData.billAmount}</th>
                          <th></th>
                        </tr>
                      </tfoot>
                    </Table>
                  </div>
                )}

                {/* Test Results Management Interface */}
                {formData.testItems.length > 0 && billSaved && (
                  <div className="mt-4">
                    <h6 className="mb-3 text-secondary">
                      <FontAwesomeIcon icon={faEdit} className="me-2" />
                      Test Results Management
                    </h6>

                    <div className="table-responsive">
                      <Table striped bordered hover className="test-results-table">
                        <thead className="table-primary">
                          <tr>
                            <th style={{ width: '25%' }}>Test Name</th>
                            <th style={{ width: '15%' }}>Result</th>
                            <th style={{ width: '15%' }}>Units</th>
                            <th style={{ width: '20%' }}>Reference Range</th>
                            <th style={{ width: '15%' }}>Status</th>
                            <th style={{ width: '10%' }}>Comments</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.testItems.map((item) => {
                            const testResult = formData.testResults?.[item.id] || {};
                            const isAbnormal = testResult.result && testResult.referenceRange &&
                              !isValueInRange(testResult.result, testResult.referenceRange);

                            return (
                              <tr key={item.id} className={isAbnormal ? 'table-warning' : ''}>
                                <td className="fw-bold">{item.name}</td>

                                {/* Editable Result Field */}
                                <td>
                                  <Form.Control
                                    type="text"
                                    size="sm"
                                    value={testResult.result || ''}
                                    onChange={(e) => handleTestResultChange(item.id, 'result', e.target.value)}
                                    placeholder="Enter result"
                                    className={`${isAbnormal ? 'border-warning' : ''}`}
                                    style={{
                                      backgroundColor: isAbnormal ? '#fff3cd' :
                                                     testResult.result ? '#d4edda' : 'white',
                                      color: isAbnormal ? '#856404' : testResult.result ? '#155724' : 'inherit'
                                    }}
                                  />
                                </td>

                                {/* Units */}
                                <td>
                                  <Form.Control
                                    type="text"
                                    size="sm"
                                    value={testResult.units || ''}
                                    onChange={(e) => handleTestResultChange(item.id, 'units', e.target.value)}
                                    placeholder="Units"
                                  />
                                </td>

                                {/* Reference Range (Non-editable) */}
                                <td>
                                  <Form.Control
                                    type="text"
                                    size="sm"
                                    value={testResult.referenceRange || 'Normal: 0-100'}
                                    onChange={(e) => handleTestResultChange(item.id, 'referenceRange', e.target.value)}
                                    placeholder="Reference range"
                                    className="bg-light"
                                  />
                                </td>

                                {/* Report Status Dropdown */}
                                <td>
                                  <Form.Select
                                    size="sm"
                                    value={testResult.status || 'pending'}
                                    onChange={(e) => handleTestResultChange(item.id, 'status', e.target.value)}
                                    className={`${
                                      testResult.status === 'approved' ? 'border-success' :
                                      testResult.status === 'rejected' ? 'border-danger' :
                                      testResult.status === 'retest' ? 'border-warning' : ''
                                    }`}
                                  >
                                    <option value="pending">Pending</option>
                                    <option value="retest">Retest</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="approved">Approved</option>
                                  </Form.Select>
                                </td>

                                {/* Editable Comments */}
                                <td>
                                  <Form.Control
                                    as="textarea"
                                    rows={1}
                                    size="sm"
                                    value={testResult.comments || ''}
                                    onChange={(e) => handleTestResultChange(item.id, 'comments', e.target.value)}
                                    placeholder="Notes"
                                    style={{ resize: 'vertical', minHeight: '32px' }}
                                  />
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </Table>
                    </div>

                    {/* Test Results Summary */}
                    <div className="mt-3 p-3 bg-light rounded">
                      <div className="row">
                        <div className="col-md-3">
                          <div className="text-center">
                            <div className="h5 text-success mb-1">
                              {formData.testItems.filter(item =>
                                formData.testResults?.[item.id]?.status === 'approved'
                              ).length}
                            </div>
                            <small className="text-muted">Approved</small>
                          </div>
                        </div>
                        <div className="col-md-3">
                          <div className="text-center">
                            <div className="h5 text-warning mb-1">
                              {formData.testItems.filter(item =>
                                formData.testResults?.[item.id]?.status === 'pending'
                              ).length}
                            </div>
                            <small className="text-muted">Pending</small>
                          </div>
                        </div>
                        <div className="col-md-3">
                          <div className="text-center">
                            <div className="h5 text-info mb-1">
                              {formData.testItems.filter(item =>
                                formData.testResults?.[item.id]?.status === 'retest'
                              ).length}
                            </div>
                            <small className="text-muted">Retest</small>
                          </div>
                        </div>
                        <div className="col-md-3">
                          <div className="text-center">
                            <div className="h5 text-danger mb-1">
                              {formData.testItems.filter(item =>
                                formData.testResults?.[item.id]?.status === 'rejected'
                              ).length}
                            </div>
                            <small className="text-muted">Rejected</small>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Approval Button */}
                    {areAllTestsReadyForApproval() && (
                      <div className="mt-3 text-center">
                        <Button
                          variant="success"
                          size="lg"
                          onClick={() => setShowApprovalModal(true)}
                          disabled={formData.reportStatus === 'approved'}
                        >
                          <FontAwesomeIcon icon={faCheck} className="me-2" />
                          {formData.reportStatus === 'approved' ? 'Report Approved' : 'Approve Report'}
                        </Button>

                        {formData.reportStatus === 'approved' && (
                          <div className="mt-2 text-success">
                            <small>
                              <FontAwesomeIcon icon={faCheck} className="me-1" />
                              Report approved on {formData.approvalHistory?.[formData.approvalHistory.length - 1]?.approvalTimestamp
                                ? new Date(formData.approvalHistory[formData.approvalHistory.length - 1].approvalTimestamp).toLocaleString()
                                : 'Unknown date'}
                            </small>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>

          {/* Right Column - Billing Details */}
          <Col lg={4}>
            {/* Billing Details */}
            <Card className="shadow mb-4">
              <Card.Header className="bg-success text-white">
                <h6 className="mb-0">Billing Details</h6>
              </Card.Header>
              <Card.Body>
                <div className="billing-summary">
                  <div className="d-flex justify-content-between mb-2">
                    <span>Bill Amount:</span>
                    <span className="fw-bold">₹{formData.billAmount}</span>
                  </div>

                  <Form.Group className="mb-3">
                    <Form.Label>Other Charges</Form.Label>
                    <InputGroup>
                      <InputGroup.Text>₹</InputGroup.Text>
                      <Form.Control
                        type="number"
                        name="otherCharges"
                        value={formData.otherCharges}
                        onChange={handleChange}
                        step="0.01"
                        min="0"
                      />
                    </InputGroup>
                  </Form.Group>

                  {/* Enhanced Discount System */}
                  <Form.Group className="mb-3">
                    <Form.Label>Discount Type</Form.Label>
                    <Form.Select
                      name="discountType"
                      value={formData.discountType}
                      onChange={handleChange}
                    >
                      <option value="percentage">Percentage (%)</option>
                      <option value="amount">Amount (₹)</option>
                    </Form.Select>
                  </Form.Group>

                  {formData.discountType === 'percentage' ? (
                    <Form.Group className="mb-3">
                      <Form.Label>Discount %</Form.Label>
                      <InputGroup>
                        <Form.Control
                          type="number"
                          name="discountPercent"
                          value={formData.discountPercent}
                          onChange={handleChange}
                          step="0.01"
                          min="0"
                          max="100"
                        />
                        <InputGroup.Text>%</InputGroup.Text>
                      </InputGroup>
                    </Form.Group>
                  ) : (
                    <Form.Group className="mb-3">
                      <Form.Label>Discount Amount</Form.Label>
                      <InputGroup>
                        <InputGroup.Text>₹</InputGroup.Text>
                        <Form.Control
                          type="number"
                          name="discountAmount"
                          value={formData.discountAmount}
                          onChange={handleChange}
                          step="0.01"
                          min="0"
                        />
                      </InputGroup>
                    </Form.Group>
                  )}

                  {/* Mandatory Discount Remarks */}
                  {(formData.discountPercent > 0 || formData.discountAmount > 0) && (
                    <Form.Group className="mb-3">
                      <Form.Label>Discount Remarks <span className="text-danger">*</span></Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={2}
                        name="discountRemarks"
                        value={formData.discountRemarks}
                        onChange={handleChange}
                        placeholder="Enter reason for discount (required)"
                        required={formData.discountPercent > 0 || formData.discountAmount > 0}
                      />
                    </Form.Group>
                  )}

                  <div className="d-flex justify-content-between mb-2 border-top pt-2">
                    <span className="fw-bold">Total Amount:</span>
                    <span className="fw-bold text-primary">₹{formData.totalAmount}</span>
                  </div>

                  {/* Dynamic Due Details Display */}
                  <div className="due-details-section mb-3 p-3 border rounded bg-light">
                    <h6 className="mb-3 text-secondary">
                      <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
                      Payment Details
                    </h6>

                    <div className="row">
                      <div className="col-md-6">
                        <div className="d-flex justify-content-between mb-2">
                          <span>Total Bill Amount:</span>
                          <span className="fw-bold">₹{formData.totalAmount}</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Amount Paid:</span>
                          <span className="fw-bold text-success">₹{formData.amountPaid}</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2 border-top pt-2">
                          <span className="fw-bold">Outstanding Balance:</span>
                          <span className={`fw-bold ${parseFloat(formData.balanceToBePaid) > 0 ? 'text-danger' : 'text-success'}`}>
                            ₹{formData.balanceToBePaid}
                          </span>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="text-center">
                          <div className="mb-2">
                            <span className="badge fs-6 p-2" style={{
                              backgroundColor: formData.paymentStatus === 'paid' ? '#28a745' :
                                             formData.paymentStatus === 'partial' ? '#ffc107' : '#dc3545',
                              color: formData.paymentStatus === 'partial' ? '#000' : '#fff'
                            }}>
                              {formData.paymentStatus === 'paid' ? 'FULLY PAID' :
                               formData.paymentStatus === 'partial' ? 'PARTIALLY PAID' : 'PAYMENT PENDING'}
                            </span>
                          </div>

                          {parseFloat(formData.balanceToBePaid) > 0 && (
                            <div className="text-muted small">
                              <FontAwesomeIcon icon={faExclamationTriangle} className="me-1 text-warning" />
                              Payment required: ₹{formData.balanceToBePaid}
                            </div>
                          )}

                          {formData.paymentStatus === 'paid' && (
                            <div className="text-success small">
                              <FontAwesomeIcon icon={faCheck} className="me-1" />
                              Payment Complete
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Payment History Section (if multiple payments) */}
                    {formData.paymentHistory && formData.paymentHistory.length > 0 && (
                      <div className="mt-3 border-top pt-3">
                        <h6 className="text-secondary mb-2">Payment History</h6>
                        <div className="payment-history" style={{ maxHeight: '150px', overflowY: 'auto' }}>
                          {formData.paymentHistory.map((payment, index) => (
                            <div key={index} className="d-flex justify-content-between align-items-center mb-1 p-2 bg-white rounded">
                              <div>
                                <small className="text-muted">{payment.date}</small>
                                <div className="fw-bold">₹{payment.amount}</div>
                              </div>
                              <div className="text-end">
                                <small className="text-muted">{payment.method}</small>
                                <div className="badge bg-success">Paid</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <Form.Group className="mb-3">
                    <Form.Label>Amount Paid</Form.Label>
                    <InputGroup>
                      <InputGroup.Text>₹</InputGroup.Text>
                      <Form.Control
                        type="number"
                        name="amountPaid"
                        value={formData.amountPaid}
                        onChange={handleChange}
                        step="0.01"
                        min="0"
                      />
                    </InputGroup>
                  </Form.Group>

                  <div className="d-flex justify-content-between mb-3 border-top pt-2">
                    <span className="fw-bold">Balance to be paid:</span>
                    <span className="fw-bold text-danger">₹{formData.balanceToBePaid}</span>
                  </div>

                  {/* Payment Status Indicator */}
                  <div className="d-flex justify-content-between mb-3">
                    <span className="fw-bold">Payment Status:</span>
                    <span className={`fw-bold ${
                      formData.paymentStatus === 'paid' ? 'text-success' :
                      formData.paymentStatus === 'partial' ? 'text-warning' : 'text-danger'
                    }`}>
                      {formData.paymentStatus === 'paid' ? 'PAID' :
                       formData.paymentStatus === 'partial' ? 'PARTIAL' : 'PENDING'}
                    </span>
                  </div>

                  {/* Report Printing Restriction */}
                  {formData.paymentStatus !== 'paid' && (
                    <div className="alert alert-warning p-2 mb-3">
                      <small>
                        <i className="fas fa-exclamation-triangle me-1"></i>
                        Report printing is disabled until full payment is received.
                      </small>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>

            {/* Payment Details */}
            <Card className="shadow mb-4">
              <Card.Header className="bg-warning text-dark">
                <h6 className="mb-0">Payment Details</h6>
              </Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Cash</Form.Label>
                  <Form.Select
                    name="cash"
                    value={formData.cash}
                    onChange={handleChange}
                  >
                    <option value="">Select Payment Mode</option>
                    <option value="Cash">Cash</option>
                    <option value="Card">Card</option>
                    <option value="UPI">UPI</option>
                    <option value="Net Banking">Net Banking</option>
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Bank Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="bankName"
                    value={formData.bankName}
                    onChange={handleChange}
                    placeholder="Bank name"
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Reference Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="referenceNumber"
                    value={formData.referenceNumber}
                    onChange={handleChange}
                    placeholder="DD/MM/YYYY"
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Amount</Form.Label>
                  <InputGroup>
                    <InputGroup.Text>₹</InputGroup.Text>
                    <Form.Control
                      type="number"
                      name="amount"
                      value={formData.amount}
                      onChange={handleChange}
                      step="0.01"
                      min="0"
                    />
                  </InputGroup>
                </Form.Group>
              </Card.Body>
            </Card>

            {/* Additional Options */}
            <Card className="shadow mb-4">
              <Card.Header className="bg-secondary text-white">
                <h6 className="mb-0">Additional Options</h6>
              </Card.Header>
              <Card.Body>
                <Form.Group className="mb-3">
                  <Form.Label>Remarks</Form.Label>
                  <Form.Select
                    name="remarks"
                    value={formData.remarks}
                    onChange={handleChange}
                  >
                    <option value="">Select Remarks</option>
                    <option value="Emergency">Emergency</option>
                    <option value="Routine">Routine</option>
                    <option value="Urgent">Urgent</option>
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>Delivery Mode</Form.Label>
                  <Form.Select
                    name="deliveryMode"
                    value={formData.deliveryMode}
                    onChange={handleChange}
                  >
                    <option value="">Select Delivery Mode</option>
                    <option value="Email">Email</option>
                    <option value="SMS">SMS</option>
                    <option value="WhatsApp">WhatsApp</option>
                    <option value="Print">Print</option>
                  </Form.Select>
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Study No</Form.Label>
                      <Form.Control
                        type="text"
                        name="studyNo"
                        value={formData.studyNo}
                        onChange={handleChange}
                        placeholder="Study No"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Sub Period</Form.Label>
                      <Form.Control
                        type="text"
                        name="subPeriod"
                        value={formData.subPeriod}
                        onChange={handleChange}
                        placeholder="Subject Period"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Sub No</Form.Label>
                      <Form.Control
                        type="text"
                        name="subNo"
                        value={formData.subNo}
                        onChange={handleChange}
                        placeholder="Subject No"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <div className="d-flex align-items-center mt-4">
                      <Form.Check
                        type="checkbox"
                        name="emergency"
                        label="Emergency"
                        checked={formData.emergency}
                        onChange={handleChange}
                        className="me-3"
                      />
                      <Form.Check
                        type="checkbox"
                        name="printBill"
                        label="Print Bill"
                        checked={formData.printBill}
                        onChange={handleChange}
                      />
                    </div>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Clinical Remarks */}
        <Row>
          <Col lg={12}>
            <Card className="shadow mb-4">
              <Card.Header className="bg-info text-white">
                <h6 className="mb-0">Clinical Remarks</h6>
              </Card.Header>
              <Card.Body>
                <Form.Group>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="clinicalRemarks"
                    value={formData.clinicalRemarks}
                    onChange={handleChange}
                    placeholder="Enter clinical remarks..."
                  />
                </Form.Group>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Payment Validation Warning */}
        {formData.paymentStatus !== 'paid' && parseFloat(formData.balanceToBePaid) > 0 && (
          <Row className="mb-3">
            <Col lg={12}>
              <Alert variant="warning" className="d-flex align-items-center">
                <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" size="lg" />
                <div className="flex-grow-1">
                  <strong>Payment Required for Report Access</strong>
                  <div className="mt-1">
                    Outstanding balance of <strong>₹{formData.balanceToBePaid}</strong> must be paid before reports can be downloaded.
                    {formData.paymentStatus === 'partial' && (
                      <span className="ms-2 text-muted">
                        (₹{formData.amountPaid} already paid)
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-end">
                  <div className="badge bg-warning text-dark fs-6">
                    {formData.paymentStatus === 'partial' ? 'PARTIAL PAYMENT' : 'PAYMENT PENDING'}
                  </div>
                </div>
              </Alert>
            </Col>
          </Row>
        )}

        {/* Action Buttons */}
        <Row>
          <Col lg={12}>
            <div className="d-flex justify-content-end gap-2">
              <Button variant="secondary" onClick={() => navigate('/billing')}>
                <FontAwesomeIcon icon={faArrowLeft} className="me-2" />
                Cancel
              </Button>

              {/* Print/Download Report Button with Payment Validation */}
              <div className="position-relative">
                <Button
                  variant={formData.paymentStatus === 'paid' ? 'info' : 'outline-secondary'}
                  type="button"
                  disabled={formData.paymentStatus !== 'paid'}
                  title={formData.paymentStatus !== 'paid' ?
                    `Payment required: ₹${formData.balanceToBePaid} outstanding` :
                    'Download Report'}
                  onClick={() => {
                    if (formData.paymentStatus === 'paid') {
                      // Handle report download
                      console.log('Downloading report...');
                    }
                  }}
                >
                  <FontAwesomeIcon
                    icon={formData.paymentStatus === 'paid' ? faPrint : faExclamationTriangle}
                    className="me-2"
                  />
                  {formData.paymentStatus === 'paid' ? 'Download Report' : 'Payment Required'}
                </Button>

                {/* Payment Warning Tooltip */}
                {formData.paymentStatus !== 'paid' && (
                  <div className="position-absolute top-100 start-50 translate-middle-x mt-1 p-2 bg-warning text-dark rounded shadow-sm"
                       style={{ fontSize: '0.75rem', whiteSpace: 'nowrap', zIndex: 1000 }}>
                    <FontAwesomeIcon icon={faExclamationTriangle} className="me-1" />
                    Outstanding: ₹{formData.balanceToBePaid}
                  </div>
                )}
              </div>

              {/* Edit Button - Only show for saved bills */}
              {billSaved && (
                <Button
                  variant="warning"
                  type="button"
                  onClick={() => {
                    setIsEditMode(!isEditMode);
                  }}
                  disabled={loading}
                >
                  <FontAwesomeIcon icon={faEdit} className="me-2" />
                  {isEditMode ? 'Cancel Edit' : 'Edit Bill'}
                </Button>
              )}

              {/* Delete Button - Only show for saved bills */}
              {billSaved && (
                <Button
                  variant="danger"
                  type="button"
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={loading}
                >
                  <FontAwesomeIcon icon={faTrash} className="me-2" />
                  Delete Bill
                </Button>
              )}

              <Button
                variant="success"
                type="submit"
                disabled={loading}
                onClick={(e) => {
                  e.preventDefault();
                  if (isEditMode && billSaved) {
                    handleUpdateBill(formData);
                  } else {
                    handleSubmit(e);
                  }
                }}
              >
                <FontAwesomeIcon icon={faSave} className="me-2" />
                {loading ? 'Saving...' : (isEditMode && billSaved ? 'Update Bill' : 'Save & Continue')}
              </Button>
            </div>
          </Col>
        </Row>
      </Form>

      {/* Advanced Patient Search Modal */}
      <Modal show={showPatientSearch} onHide={() => setShowPatientSearch(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faSearch} className="me-2" />
            Advanced Patient Search
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form.Group className="mb-3">
            <Form.Label>Search Patients</Form.Label>
            <InputGroup>
              <Form.Control
                type="text"
                placeholder="Enter Patient ID, Name, or Mobile Number..."
                value={patientSearchQuery}
                onChange={(e) => setPatientSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handlePatientSearch()}
              />
              <Button
                variant="primary"
                onClick={handlePatientSearch}
                disabled={searchLoading}
              >
                <FontAwesomeIcon icon={faSearch} className="me-1" />
                {searchLoading ? 'Searching...' : 'Search'}
              </Button>
            </InputGroup>
          </Form.Group>

          {/* Search Results */}
          {patientSearchResults.length > 0 && (
            <div>
              <h6>Search Results ({patientSearchResults.length} found)</h6>
              <ListGroup style={{ maxHeight: '400px', overflowY: 'auto' }}>
                {patientSearchResults.map((patient) => (
                  <ListGroup.Item
                    key={patient.id}
                    action
                    onClick={() => handlePatientSelect(patient)}
                    className="d-flex justify-content-between align-items-start"
                  >
                    <div>
                      <div className="fw-bold">
                        {patient.first_name} {patient.last_name}
                      </div>
                      <div className="text-muted">
                        <small>
                          Patient ID: {patient.patient_id || patient.id} |
                          Mobile: {patient.phone} |
                          DOB: {patient.date_of_birth || 'N/A'}
                        </small>
                      </div>
                      {patient.email && (
                        <div className="text-muted">
                          <small>Email: {patient.email}</small>
                        </div>
                      )}
                    </div>
                    <Button variant="outline-primary" size="sm">
                      Select
                    </Button>
                  </ListGroup.Item>
                ))}
              </ListGroup>
            </div>
          )}

          {patientSearchQuery && patientSearchResults.length === 0 && !searchLoading && (
            <Alert variant="info" className="d-flex justify-content-between align-items-center">
              <span>No patients found matching your search criteria.</span>
              <Button
                variant="primary"
                size="sm"
                onClick={() => {
                  // Pre-fill patient add form with search query if it looks like a name
                  const searchTerms = patientSearchQuery.trim().split(' ');
                  if (searchTerms.length >= 2) {
                    setPatientAddData(prev => ({
                      ...prev,
                      first_name: searchTerms[0],
                      last_name: searchTerms.slice(1).join(' ')
                    }));
                  } else if (searchTerms.length === 1) {
                    // If single term, put it in first name
                    setPatientAddData(prev => ({
                      ...prev,
                      first_name: searchTerms[0]
                    }));
                  }
                  setShowPatientSearch(false);
                  handlePatientAddOpen();
                }}
              >
                <FontAwesomeIcon icon={faUserPlus} className="me-1" />
                Add New Patient
              </Button>
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPatientSearch(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Patient Add Modal */}
      <Modal show={showPatientAdd} onHide={() => setShowPatientAdd(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faUserPlus} className="me-2" />
            Add New Patient
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && (
            <Alert variant="danger" className="mb-4">
              {error}
            </Alert>
          )}

          <Form noValidate validated={patientAddValidated} onSubmit={handlePatientAddSubmit}>
            {/* Site Code Selection for Admin/Hub Admin */}
            {(currentUser?.role === 'admin' || currentUser?.role === 'hub_admin') && (
              <Row className="mb-3">
                <Col md={12}>
                  <Form.Group controlId="tenant_id">
                    <Form.Label>Franchise/Site Code <span className="text-danger">*</span></Form.Label>
                    <Form.Select
                      name="tenant_id"
                      value={patientAddData.tenant_id || ''}
                      onChange={handlePatientAddChange}
                      required
                    >
                      <option value="">Select Franchise</option>
                      {branches.map(branch => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name} ({branch.site_code})
                        </option>
                      ))}
                    </Form.Select>
                    <Form.Control.Feedback type="invalid">
                      Please select a franchise.
                    </Form.Control.Feedback>
                  </Form.Group>
                </Col>
              </Row>
            )}

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="first_name">
                  <Form.Label>First Name <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="text"
                    name="first_name"
                    value={patientAddData.first_name}
                    onChange={handlePatientAddChange}
                    required
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter first name.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="last_name">
                  <Form.Label>Last Name <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="text"
                    name="last_name"
                    value={patientAddData.last_name}
                    onChange={handlePatientAddChange}
                    required
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter last name.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="gender">
                  <Form.Label>Gender <span className="text-danger">*</span></Form.Label>
                  <Form.Select
                    name="gender"
                    value={patientAddData.gender}
                    onChange={handlePatientAddChange}
                    required
                  >
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </Form.Select>
                  <Form.Control.Feedback type="invalid">
                    Please select gender.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="date_of_birth">
                  <Form.Label>Date of Birth <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="date"
                    name="date_of_birth"
                    value={patientAddData.date_of_birth}
                    onChange={handlePatientAddChange}
                    required
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter date of birth.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="phone">
                  <Form.Label>Phone <span className="text-danger">*</span></Form.Label>
                  <Form.Control
                    type="tel"
                    name="phone"
                    value={patientAddData.phone}
                    onChange={handlePatientAddChange}
                    required
                  />
                  <Form.Control.Feedback type="invalid">
                    Please enter phone number.
                  </Form.Control.Feedback>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="email">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={patientAddData.email}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3" controlId="address">
              <Form.Label>Address</Form.Label>
              <Form.Control
                as="textarea"
                rows={2}
                name="address"
                value={patientAddData.address}
                onChange={handlePatientAddChange}
              />
            </Form.Group>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3" controlId="city">
                  <Form.Label>City</Form.Label>
                  <Form.Control
                    type="text"
                    name="city"
                    value={patientAddData.city}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3" controlId="state">
                  <Form.Label>State</Form.Label>
                  <Form.Control
                    type="text"
                    name="state"
                    value={patientAddData.state}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3" controlId="postal_code">
                  <Form.Label>Postal Code</Form.Label>
                  <Form.Control
                    type="text"
                    name="postal_code"
                    value={patientAddData.postal_code}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <h6 className="mt-4 mb-3 font-weight-bold">Additional Information</h6>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="emergency_contact">
                  <Form.Label>Emergency Contact</Form.Label>
                  <Form.Control
                    type="text"
                    name="emergency_contact"
                    value={patientAddData.emergency_contact}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3" controlId="emergency_phone">
                  <Form.Label>Emergency Phone</Form.Label>
                  <Form.Control
                    type="tel"
                    name="emergency_phone"
                    value={patientAddData.emergency_phone}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={4}>
                <Form.Group className="mb-3" controlId="blood_group">
                  <Form.Label>Blood Group</Form.Label>
                  <Form.Select
                    name="blood_group"
                    value={patientAddData.blood_group}
                    onChange={handlePatientAddChange}
                  >
                    <option value="">Select Blood Group</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3" controlId="insurance_provider">
                  <Form.Label>Insurance Provider</Form.Label>
                  <Form.Control
                    type="text"
                    name="insurance_provider"
                    value={patientAddData.insurance_provider}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3" controlId="insurance_id">
                  <Form.Label>Insurance ID</Form.Label>
                  <Form.Control
                    type="text"
                    name="insurance_id"
                    value={patientAddData.insurance_id}
                    onChange={handlePatientAddChange}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPatientAdd(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handlePatientAddSubmit}
            disabled={patientAddLoading}
          >
            <FontAwesomeIcon icon={faSave} className="me-2" />
            {patientAddLoading ? 'Saving...' : 'Save Patient'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteConfirm} onHide={() => setShowDeleteConfirm(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-danger">
            <FontAwesomeIcon icon={faTrash} className="me-2" />
            Confirm Deletion
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="text-center">
            <FontAwesomeIcon icon={faExclamationTriangle} size="3x" className="text-warning mb-3" />
            <h5>Are you sure you want to delete this bill?</h5>
            <p className="text-muted">
              This action cannot be undone. All billing information and associated data will be permanently removed.
            </p>
            {formData.paymentStatus === 'paid' && (
              <Alert variant="warning" className="mt-3">
                <strong>Warning:</strong> This bill has been fully paid. Deleting it may affect financial records.
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteConfirm(false)}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleDeleteBill}
            disabled={loading}
          >
            <FontAwesomeIcon icon={faTrash} className="me-2" />
            {loading ? 'Deleting...' : 'Delete Bill'}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Report Approval Modal */}
      <Modal show={showApprovalModal} onHide={() => setShowApprovalModal(false)} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title className="text-success">
            <FontAwesomeIcon icon={faCheck} className="me-2" />
            Report Approval
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* Test Results Summary */}
          <div className="mb-4">
            <h6 className="text-secondary mb-3">Test Results Summary</h6>
            <div className="table-responsive">
              <Table striped bordered size="sm">
                <thead className="table-light">
                  <tr>
                    <th>Test Name</th>
                    <th>Result</th>
                    <th>Units</th>
                    <th>Reference Range</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.testItems.map((item) => {
                    const testResult = formData.testResults?.[item.id] || {};
                    const isAbnormal = testResult.result && testResult.referenceRange &&
                      !isValueInRange(testResult.result, testResult.referenceRange);

                    return (
                      <tr key={item.id} className={isAbnormal ? 'table-warning' : ''}>
                        <td className="fw-bold">{item.name}</td>
                        <td className={isAbnormal ? 'text-warning fw-bold' : 'text-success'}>
                          {testResult.result || 'N/A'}
                        </td>
                        <td>{testResult.units || 'N/A'}</td>
                        <td>{testResult.referenceRange || 'N/A'}</td>
                        <td>
                          <span className={`badge ${
                            testResult.status === 'approved' ? 'bg-success' :
                            testResult.status === 'rejected' ? 'bg-danger' :
                            testResult.status === 'retest' ? 'bg-warning' : 'bg-secondary'
                          }`}>
                            {testResult.status || 'pending'}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </Table>
            </div>
          </div>

          {/* Approval Form */}
          <div className="border-top pt-4">
            <h6 className="text-secondary mb-3">Approval Details</h6>

            <Form.Group className="mb-3">
              <Form.Label>Approver Name *</Form.Label>
              <Form.Control
                type="text"
                value={approvalData.approverName}
                onChange={(e) => setApprovalData(prev => ({...prev, approverName: e.target.value}))}
                placeholder="Enter approver name"
                required
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Digital Signature</Form.Label>
              <Form.Control
                type="text"
                value={approvalData.approverSignature}
                onChange={(e) => setApprovalData(prev => ({...prev, approverSignature: e.target.value}))}
                placeholder="Enter digital signature or ID"
              />
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Approval Comments</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={approvalData.approvalComments}
                onChange={(e) => setApprovalData(prev => ({...prev, approvalComments: e.target.value}))}
                placeholder="Enter any additional comments or notes"
              />
            </Form.Group>

            <div className="bg-light p-3 rounded">
              <div className="row">
                <div className="col-md-6">
                  <strong>Approval Timestamp:</strong>
                  <div className="text-muted">{new Date().toLocaleString()}</div>
                </div>
                <div className="col-md-6">
                  <strong>Patient:</strong>
                  <div className="text-muted">{formData.patientName}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Approval History */}
          {formData.approvalHistory && formData.approvalHistory.length > 0 && (
            <div className="mt-4 border-top pt-4">
              <h6 className="text-secondary mb-3">Approval History</h6>
              <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                {formData.approvalHistory.map((approval, index) => (
                  <div key={index} className="mb-2 p-2 bg-light rounded">
                    <div className="d-flex justify-content-between">
                      <strong>{approval.approverName}</strong>
                      <small className="text-muted">
                        {new Date(approval.approvalTimestamp).toLocaleString()}
                      </small>
                    </div>
                    {approval.approvalComments && (
                      <div className="text-muted small mt-1">{approval.approvalComments}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowApprovalModal(false)}>
            Cancel
          </Button>
          <Button
            variant="success"
            onClick={handleApprovalSubmit}
            disabled={loading || !approvalData.approverName}
          >
            <FontAwesomeIcon icon={faCheck} className="me-2" />
            {loading ? 'Approving...' : 'Approve Report'}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default BillingRegistration;
